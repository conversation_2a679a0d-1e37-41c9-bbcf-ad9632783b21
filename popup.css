/* ===== Popup styles ===== */
:root {
  --bg: #ffffff;
  --fg: #1f2937;
  --muted: #6b7280;
  --border: #e5e7eb;
  --accent: #111827;
  --danger: #991b1b;
}

* { box-sizing: border-box; }

html, body {
  margin: 0;
  padding: 0;
  background: var(--bg);
  color: var(--fg);
  font: 14px/1.4 system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
}

/* Constrain popup size for consistency - made wider for table */
body { width: 1200px; min-height: 400px; }

.px-12 { padding-left: 12px; padding-right: 12px; }
.py-10 { padding-top: 10px; padding-bottom: 10px; }
.pb-12 { padding-bottom: 12px; }

header {
  border-bottom: 1px solid var(--border);
  background: #fafafa;
}
.title {
  margin: 0 0 2px 0;
  font-size: 16px;
  font-weight: 700;
}
.subtitle {
  margin: 0;
  color: var(--muted);
  font-size: 12px;
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.count { font-weight: 600; }

.btn {
  appearance: none;
  border: 1px solid var(--border);
  background: #f9fafb;
  color: var(--accent);
  padding: 4px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 120ms ease, border-color 120ms ease;
  margin-left: 6px;
}
.btn:hover { background: #f3f4f6; border-color: #d1d5db; }
.btn.danger { color: #fff; background: #ef4444; border-color: #ef4444; }
.btn.danger:hover { background: #dc2626; border-color: #dc2626; }

/* Table styles */
.table-container {
  overflow-x: auto;
  border: 1px solid var(--border);
  border-radius: 8px;
  background: white;
}

.trips-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.trips-table th {
  background: #f8f9fa;
  border: 1px solid var(--border);
  padding: 8px 6px;
  text-align: left;
  font-weight: 600;
  font-size: 11px;
  white-space: nowrap;
  position: sticky;
  top: 0;
  z-index: 10;
}

.trips-table td {
  border: 1px solid var(--border);
  padding: 6px;
  vertical-align: top;
  max-width: 120px;
  word-wrap: break-word;
}

.trips-table tr:nth-child(even) {
  background: #fafafa;
}

.trips-table tr:hover {
  background: #f0f9ff;
}

/* Editable cells */
.editable {
  background: transparent;
  border: none;
  width: 100%;
  padding: 2px;
  font-size: 12px;
  font-family: inherit;
  resize: none;
  min-height: 20px;
}

.editable:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
  background: white;
}

.editable.comment {
  min-height: 40px;
}

/* Action buttons */
.remove-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
}

.remove-btn:hover {
  background: #dc2626;
}

/* Legacy card styles - kept for compatibility but not used in table layout */

.empty {
  text-align: center;
  padding: 24px 6px 16px 6px;
  color: var(--muted);
  border: 1px dashed var(--border);
  border-radius: 10px;
  background: #fafafa;
}
.empty-icon { font-size: 28px; margin-bottom: 6px; }
.empty-title { font-weight: 700; margin-bottom: 4px; }
.empty-text { font-size: 12px; }

.tech {
  margin-top: 12px;
  border-top: 1px solid var(--border);
  padding-top: 10px;
}
.tech-title {
  font-weight: 700;
  margin-bottom: 6px;
}
.kv {
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: 6px;
  padding: 2px 0;
}
.kv .k { color: var(--muted); font-size: 12px; }
.kv .v { font-size: 12px; }
code { background: #f3f4f6; padding: 1px 4px; border-radius: 4px; }
