<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>Sirev Proposal Generator</title>
    <link rel="stylesheet" href="popup.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
  </head>
  <body>
    <header class="px-12 py-10">
      <h1 class="title">Sirev Proposal Generator</h1>
      <div class="subtitle">Selected rooms from <strong>sirev.com</strong> — toggle via the checkbox next to <strong>Grtot</strong></div>
    </header>

    <main id="app" class="px-12 pb-12">
      <!-- Empty state -->
      <section id="empty" class="empty" hidden>
        <div class="empty-icon">🗒️</div>
        <div class="empty-title">No selections yet</div>
        <div class="empty-text">
          Go to <strong>sirev.com</strong>, and check the box next to a room’s <strong>Grtot</strong> value.
        </div>
      </section>

      <!-- List -->
      <section id="list" class="list" hidden>
        <div class="list-header">
          <div class="count"><span id="count">0</span> selected</div>
          <div class="actions">
            <button id="btnRefresh" type="button" class="btn">Refresh</button>
            <button id="btnClear" type="button" class="btn danger">Clear all</button>
          </div>
        </div>
        <div class="table-container">
          <table id="tripsTable" class="trips-table">
            <thead>
              <tr>
                <th>Hotel Name</th>
                <th>Location</th>
                <th>Nights</th>
                <th>Flight To</th>
                <th>Check-in</th>
                <th>Check-out</th>
                <th>Flight #</th>
                <th>Grtot</th>
                <th>Return Date</th>
                <th>Dep. Time</th>
                <th>Arr. Time</th>
                <th>Comment</th>
                <th>Photos/Videos</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="items">
              <!-- Trip rows will be inserted here -->
            </tbody>
          </table>
        </div>
      </section>

      <!-- Technical Info -->
      <section id="tech" class="tech">
        <div class="tech-title">Technical info</div>
        <div class="kv">
          <div class="k">Extension</div>
          <div class="v"><span id="extName">Sirev Proposal Generator</span> <span id="extVer">0.3.0</span></div>
        </div>
        <div class="kv">
          <div class="k">Storage key</div>
          <div class="v"><code id="storageKey">lscSelectedRooms</code></div>
        </div>
        <div class="kv">
          <div class="k">Selected count</div>
          <div class="v" id="techCount">0</div>
        </div>
        <div class="kv">
          <div class="k">Bytes in use</div>
          <div class="v" id="bytesInUse">—</div>
        </div>
        <div class="kv">
          <div class="k">Loaded at</div>
          <div class="v" id="loadedAt">—</div>
        </div>
      </section>
    </main>

    <script src="popup.js"></script>
  </body>
</html>
