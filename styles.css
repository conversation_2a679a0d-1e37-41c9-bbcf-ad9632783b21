/* ===== Sirev Proposal Generator — Styles ===== */

/* Legacy button styles kept for safety (unused now) */
.lsc-include-btn {
  display: inline-block;
  margin-left: 6px;
  padding: 2px 8px;
  font-size: 12px;
  line-height: 18px;
  border: 1px solid #8a8a8a;
  border-radius: 12px;
  background: #f6f6f6;
  color: #333;
  cursor: pointer;
  user-select: none;
  vertical-align: middle;
  transition: background 120ms ease, color 120ms ease, border-color 120ms ease;
}
.lsc-include-btn:hover { background: #ececec; }
.lsc-include-btn.selected,
.lsc-include-btn[aria-pressed="true"] {
  background: #16a34a; /* green */
  color: #fff;
  border-color: #0f8a3d;
}

/* New: pretty checkbox */
.lsc-checkbox {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  margin-left: 6px;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
}

.lsc-checkbox input[type="checkbox"] {
  appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid #8a8a8a;
  border-radius: 4px;
  background: #fff;
  position: relative;
  transition: background 120ms ease, border-color 120ms ease;
}

.lsc-checkbox input[type="checkbox"]:hover {
  border-color: #6b7280;
}

.lsc-checkbox input[type="checkbox"]:checked {
  background: #16a34a;
  border-color: #0f8a3d;
}

.lsc-checkbox input[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  left: 3px;
  top: 0px;
  width: 6px;
  height: 10px;
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
  transform: rotate(45deg);
}

.lsc-checkbox .label {
  font-size: 12px;
  color: #333;
}
.lsc-checkbox input[type="checkbox"]:checked + .label {
  color: #0f8a3d;
  font-weight: 600;
}

/* Keep layout tidy even inside tight tables */
.lsc-inline-wrap { white-space: nowrap; }

/* Optional tiny badge (unused) */
.lsc-badge {
  display: inline-block;
  padding: 1px 6px;
  font-size: 10px;
  border-radius: 999px;
  background: #eee;
  color: #333;
}
