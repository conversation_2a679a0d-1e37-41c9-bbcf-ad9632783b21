/* ===== Sirev Proposal Generator — Content Script (sirev.com) =====
 * - Injects a nice checkbox next to each Grtot value.
 * - Toggles selected state and persists to chrome.storage.local.
 * - Logs key actions and all errors to the console.
 */

(() => {
  const EXT_NAME = "Sirev Proposal Generator";
  const EXT_VER = "0.3.0";
  const DEBUG = true; // always log for easier testing

  const LOG_PREFIX = `[${EXT_NAME} CS]`;

  const log = (...args) => DEBUG && console.log(LOG_PREFIX, ...args);
  const info = (...args) => DEBUG && console.info(LOG_PREFIX, ...args);
  const warn = (...args) => DEBUG && console.warn(LOG_PREFIX, ...args);
  const error = (...args) => console.error(LOG_PREFIX, ...args);

  // Global error capture
  window.addEventListener("error", (e) => {
    error("Uncaught error:", e.message, e.filename, e.lineno, e.colno, e.error || "");
  });
  window.addEventListener("unhandledrejection", (e) => {
    error("Unhandled promise rejection:", e.reason);
  });

  // Only run on sirev.com
  try {
    if (!/(^|\.)sirev\.com$/i.test(location.hostname)) {
      info("Not on sirev.com; exiting.", { hostname: location.hostname });
      return;
    }
  } catch (e) {
    error("Hostname check failed:", e);
    return;
  }

  const EXT_KEY = "lscSelectedRooms"; // storage key: { [roomKey]: roomData }

  // Simple stable hash to build a compact room key from text
  const hash = (str) => {
    let h = 2166136261 >>> 0; // FNV-1a
    for (let i = 0; i < str.length; i++) {
      h ^= str.charCodeAt(i);
      h = Math.imul(h, 16777619);
    }
    return "h" + (h >>> 0).toString(36);
  };

  const loadSelectedMap = () =>
    new Promise((resolve) => {
      chrome.storage.local.get([EXT_KEY], (res) => {
        const map = res[EXT_KEY] || {};
        log("Loaded selection map", { count: Object.keys(map).length, map });
        resolve(map);
      });
    });

  const saveSelectedMap = (map) =>
    new Promise((resolve) => {
      chrome.storage.local.set({ [EXT_KEY]: map }, () => {
        log("Saved selection map", { count: Object.keys(map).length, map });
        resolve();
      });
    });

  const text = (node) => (node?.textContent || "").trim();

  function findResultTables() {
    try {
      const tables = Array.from(document.querySelectorAll("table.search"));
      const result = tables.filter((tbl) => {
        const th = tbl.querySelector("thead");
        if (!th) return false;
        const headers = Array.from(th.querySelectorAll("td,th"));
        return headers.some((h) => text(h).toLowerCase().includes("grtot"));
      });
      info("Found result tables with 'Grtot' header", { totalTables: tables.length, matched: result.length });
      return result;
    } catch (e) {
      error("findResultTables failed:", e);
      return [];
    }
  }

  function getGrtotIndex(table) {
    try {
      const th = table.querySelector("thead");
      if (!th) {
        warn("Table has no THEAD; skipping.", table);
        return -1;
      }
      const headers = Array.from(th.querySelectorAll("td,th"));
      const idx = headers.findIndex((h) => text(h).toLowerCase() === "grtot" || text(h).toLowerCase().includes("grtot"));
      if (idx === -1) {
        warn("Could not find exact 'Grtot' header; headers are:", headers.map((h) => text(h)));
      } else {
        log("Grtot column index detected", { index: idx, headers: headers.map((h) => text(h)) });
      }
      return idx;
    } catch (e) {
      error("getGrtotIndex failed:", e);
      return -1;
    }
  }

  function nearestHotelRoot(row) {
    try {
      const root = row.closest('table[id^="hotel-"]');
      return root || null;
    } catch {
      return null;
    }
  }

  function buildRoomKey(table, row, grtotCell) {
    try {
      const hotelRoot = nearestHotelRoot(row);
      const hotelIdMatch = hotelRoot?.id?.match(/^hotel-(\d+)/);
      const hotelId = hotelIdMatch ? hotelIdMatch[1] : "unknown";

      // Room desc cell: best-effort — in sample markup it's 2nd col of the primary row
      const roomDescCell = row.querySelector("td:nth-child(2)");
      const roomDesc = text(roomDescCell) || "";

      // Nights: often 3rd cell of the primary row
      const nightsCell = row.querySelector("td:nth-child(3)");
      const nightsTxt = text(nightsCell) || "";

      const displayed = (grtotCell.querySelector("a")?.textContent || grtotCell.textContent || "")
        .replace(/\s+/g, " ")
        .trim();

      const raw = JSON.stringify({ hotelId, roomDesc, nightsTxt, displayed });
      const key = hash(raw);
      log("Built room key", { key, raw: JSON.parse(raw) });
      return key;
    } catch (e) {
      error("buildRoomKey failed:", e);
      return "h-fallback-" + Math.random().toString(36).slice(2);
    }
  }

  function extractRoomData(table, row, grtotCell) {
    try {
      const hotelRoot = nearestHotelRoot(row);

      // Extract hotel name and location from the hotel header
      const nameNode = hotelRoot?.querySelector('td[style*="font-weight: bold"][style*="font-size: 17px"]');
      let hotelName = "Unknown Hotel";
      let location = "Unknown Location";

      if (nameNode) {
        const hotelText = nameNode.innerHTML || "";
        // Extract hotel name (first text node before <br>)
        const hotelNameMatch = hotelText.match(/^([^<]+)/);
        if (hotelNameMatch) {
          hotelName = hotelNameMatch[1].trim();
        }

        // Extract location from span after <br> (before parentheses)
        const locationMatch = hotelText.match(/<span[^>]*>([^(]+)/);
        if (locationMatch) {
          location = locationMatch[1].trim();
        }
      }

      // Extract room description
      const roomDescCell = row.querySelector("td:nth-child(2)");
      const roomDesc = roomDescCell ? (roomDescCell.textContent || "").replace(/\s+/g, ' ').trim() : "Unknown Room";

      // Extract number of nights
      const nightsCell = row.querySelector("td:nth-child(3)");
      const nights = nightsCell ? (nightsCell.textContent || "").trim() : "?";

      // Extract flight to destination (VAC, CUN, etc.)
      const flightToCell = row.querySelector("td:nth-child(4)");
      const flightTo = flightToCell ? (flightToCell.textContent || "").trim() : "";

      // Extract outbound flight information from current row
      const cells = Array.from(row.querySelectorAll("td"));
      let outboundDate = "";
      let outboundFlight = "";
      let outboundDepTime = "";
      let outboundArrTime = "";
      let returnDate = "";
      let returnFlight = "";
      let returnDepTime = "";
      let returnArrTime = "";

      // Find outbound flight data in current row
      cells.forEach((cell, index) => {
        const cellText = (cell.textContent || "").trim();
        const cellStyle = cell.getAttribute('style') || "";
        const cellClass = cell.getAttribute('class') || "";

        // Check for outbound date (ligne-point-itin class, width:58px)
        if (cellClass.includes('ligne-point-itin') && cellStyle.includes('width:58px') && cellText.match(/[A-Z]{3}\s+\d+/)) {
          outboundDate = cellText;
        }
        // Check for outbound flight number (ligne-point-itin class, width:57px)
        if (cellClass.includes('ligne-point-itin') && cellStyle.includes('width:57px') && cellText.match(/[A-Z]{2}\d+/)) {
          outboundFlight = cellText.trim();
        }
        // Check for outbound times (ligne-point-itin class, width:37px)
        if (cellClass.includes('ligne-point-itin') && cellStyle.includes('width:37px') && cellText.match(/\d{1,2}:\d{2}/)) {
          if (!outboundDepTime) {
            outboundDepTime = cellText;
          } else if (!outboundArrTime) {
            outboundArrTime = cellText;
          }
        }
      });

      // Look for return flight info in the next row
      const nextRow = row.nextElementSibling;
      if (nextRow) {
        const nextCells = Array.from(nextRow.querySelectorAll("td"));
        nextCells.forEach((cell, index) => {
          const cellText = (cell.textContent || "").trim();
          const cellStyle = cell.getAttribute('style') || "";
          const cellClass = cell.getAttribute('class') || "";

          // Check for return date (ligne-point class, width:58px)
          if (cellClass.includes('ligne-point') && cellStyle.includes('width:58px') && cellText.match(/[A-Z]{3}\s+\d+/)) {
            returnDate = cellText;
          }
          // Check for return flight number (ligne-point class, width:57px)
          if (cellClass.includes('ligne-point') && cellStyle.includes('width:57px') && cellText.match(/[A-Z]{2}\d+/)) {
            returnFlight = cellText.trim();
          }
          // Check for return times (ligne-point class, width:37px)
          if (cellClass.includes('ligne-point') && cellStyle.includes('width:37px') && cellText.match(/\d{1,2}:\d{2}/)) {
            if (!returnDepTime) {
              returnDepTime = cellText;
            } else if (!returnArrTime) {
              returnArrTime = cellText;
            }
          }
        });
      }

      // Extract Grtot price
      const grtotValue = (grtotCell.querySelector("a")?.textContent || grtotCell.textContent || "").trim();

      const data = {
        hotelName,
        location,
        roomDesc,
        nights,
        flightTo,
        outboundDate,
        outboundDepTime,
        outboundFlight,
        outboundArrTime,
        returnDate,
        returnDepTime,
        returnFlight,
        returnArrTime,
        grtot: grtotValue,
        comment: "", // Initialize empty comment field
        url: location.href
      };
      log("Extracted room data", data);
      return data;
    } catch (e) {
      error("extractRoomData failed:", e);
      return {
        hotelName: "Unknown Hotel",
        location: "Unknown Location",
        roomDesc: "Unknown Room",
        nights: "?",
        checkInDate: "",
        checkOutDate: "",
        flightTo: "",
        flightNumber: "",
        timeDeparture: "",
        timeArrival: "",
        returnDate: "",
        returnFlightNumber: "",
        returnTimeDeparture: "",
        returnTimeArrival: "",
        grtot: "?",
        comment: "",
        photosVideos: "",
        url: location.href
      };
    }
  }

  function ensureCheckbox(selectedMap, table, row, grtotCell) {
    try {
      if (!grtotCell) return;
      if (grtotCell.dataset.lscEnhanced === "1") {
        log("Control already injected; skipping this cell.");
        return;
      }

      const key = buildRoomKey(table, row, grtotCell);
      const data = extractRoomData(table, row, grtotCell);
      const isSelected = Boolean(selectedMap[key]);

      // Wrap existing content so we can append a control cleanly
      const wrap = document.createElement("span");
      wrap.className = "lsc-inline-wrap";
      while (grtotCell.firstChild) wrap.appendChild(grtotCell.firstChild);
      grtotCell.appendChild(wrap);

      // Build checkbox + label
      const label = document.createElement("label");
      label.className = "lsc-checkbox";
      const cb = document.createElement("input");
      cb.type = "checkbox";
      cb.checked = isSelected;
      const lbl = document.createElement("span");
      lbl.className = "label";
      lbl.textContent = "";

      label.appendChild(cb);
      label.appendChild(lbl);
      wrap.appendChild(label);

      cb.addEventListener("change", async (e) => {
        try {
          const checked = e.target.checked;
          log("Checkbox toggled", { key, checked });
          const map = await loadSelectedMap();
          if (checked) {
            map[key] = data;
            info("Room selected", { key, data });
          } else {
            delete map[key];
            info("Room unselected", { key });
          }
          await saveSelectedMap(map);
        } catch (err) {
          error("Checkbox change handler failed:", err);
        }
      });

      grtotCell.dataset.lscEnhanced = "1";
      log("Injected checkbox into Grtot cell.");
    } catch (e) {
      error("ensureCheckbox failed:", e);
    }
  }

  async function enhanceAll() {
    console.groupCollapsed(`${LOG_PREFIX} enhanceAll`);
    console.time(`${LOG_PREFIX} enhanceAll time`);
    try {
      const selectedMap = await loadSelectedMap();
      const tables = findResultTables();

      tables.forEach((table, tIndex) => {
        try {
          const gi = getGrtotIndex(table);
          if (gi < 0) {
            warn("Skipping table; no Grtot index.", { tableIndex: tIndex });
            return;
          }

          const rows = Array.from(table.querySelectorAll("tbody > tr"));
          log("Processing table rows", { tableIndex: tIndex, rowCount: rows.length, grtotIndex: gi });

          rows.forEach((row, rIndex) => {
            try {
              const cells = Array.from(row.querySelectorAll("td"));
              if (!cells.length) return;

              // Some secondary rows have fewer columns; skip if the Grtot cell is absent
              if (cells.length <= gi) {
                return;
              }
              const grtotCell = cells[gi];

              // Heuristic guard: require some visible text/content in the Grtot cell
              const cellText = (grtotCell.textContent || "").trim();
              if (!cellText) return;

              ensureCheckbox(selectedMap, table, row, grtotCell);
            } catch (rowErr) {
              error("Row processing failed:", { tableIndex: tIndex, rowIndex: rIndex, row }, rowErr);
            }
          });
        } catch (tblErr) {
          error("Table processing failed:", { tableIndex: tIndex, table }, tblErr);
        }
      });

      info("Enhancement pass complete.");
    } catch (e) {
      error("enhanceAll failed:", e);
    } finally {
      console.timeEnd(`${LOG_PREFIX} enhanceAll time`);
      console.groupEnd();
    }
  }

  function startObserver() {
    try {
      const observer = new MutationObserver((mutations) => {
        const added = mutations.reduce((acc, m) => acc + (m.addedNodes?.length || 0), 0);
        if (added > 0) {
          log("MutationObserver detected new nodes; re-enhancing.", { addedNodes: added, mutationCount: mutations.length });
          clearTimeout(startObserver._debounce);
          startObserver._debounce = setTimeout(() => {
            enhanceAll();
          }, 150);
        }
      });

      observer.observe(document.documentElement, { childList: true, subtree: true });
      info("MutationObserver started.");
      return observer;
    } catch (e) {
      error("startObserver failed:", e);
      return null;
    }
  }

  function injectInlineStyleFallback() {
    try {
      if (document.querySelector('link[href$="styles.css"]')) {
        log("External CSS present; inline fallback not needed.");
        return;
      }
      const s = document.createElement("style");
      s.textContent = `
        .lsc-checkbox{display:inline-flex;align-items:center;gap:6px;margin-left:6px;vertical-align:middle;cursor:pointer;user-select:none}
        .lsc-checkbox input[type="checkbox"]{appearance:none;width:16px;height:16px;border:1px solid #8a8a8a;border-radius:4px;background:#fff;position:relative;transition:background .12s,border-color .12s}
        .lsc-checkbox input[type="checkbox"]:hover{border-color:#6b7280}
        .lsc-checkbox input[type="checkbox"]:checked{background:#16a34a;border-color:#0f8a3d}
        .lsc-checkbox input[type="checkbox"]:checked::after{content:"";position:absolute;left:3px;top:0px;width:6px;height:10px;border:2px solid #fff;border-top:0;border-left:0;transform:rotate(45deg)}
        .lsc-checkbox .label{font-size:12px;color:#333}
        .lsc-inline-wrap{white-space:nowrap}
      `;
      document.head.appendChild(s);
      info("Injected inline CSS fallback.");
    } catch (e) {
      error("injectInlineStyleFallback failed:", e);
    }
  }

  // Debug helpers
  window.lscDump = async () => {
    const map = await loadSelectedMap();
    console.log(LOG_PREFIX, "DUMP selected map →", map);
    return map;
  };
  window.lscClear = async () => {
    await saveSelectedMap({});
    console.log(LOG_PREFIX, "CLEARED selection map.");
  };

  // Boot
  (async function init() {
    info(`Init v${EXT_VER}`, { url: location.href });
    injectInlineStyleFallback();
    await enhanceAll();
    startObserver();
    info("Ready.");
  })();
})();
