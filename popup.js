/* ===== Popup (browser action) — Sirev Proposal Generator =====
 * Shows selected rooms and logs key actions/errors to console.
 * Now also listens to storage changes so selections appear immediately.
 */

(() => {
  const EXT_NAME = "Sirev Proposal Generator";
  const EXT_VER = "0.3.0";
  const STORAGE_KEY = "lscSelectedRooms";

  const LOG_PREFIX = `[${EXT_NAME} Popup]`;
  const log = (...a) => console.log(LOG_PREFIX, ...a);
  const info = (...a) => console.info(LOG_PREFIX, ...a);
  const warn = (...a) => console.warn(LOG_PREFIX, ...a);
  const error = (...a) => console.error(LOG_PREFIX, ...a);

  // Global error capture
  window.addEventListener("error", (e) => error("Uncaught:", e.message, e.filename, e.lineno, e.colno, e.error || ""));
  window.addEventListener("unhandledrejection", (e) => error("Unhandled rejection:", e.reason));

  // UI refs
  const $empty = document.getElementById("empty");
  const $list = document.getElementById("list");
  const $items = document.getElementById("items");
  const $count = document.getElementById("count");
  const $techCount = document.getElementById("techCount");
  const $bytesInUse = document.getElementById("bytesInUse");
  const $loadedAt = document.getElementById("loadedAt");
  const $extName = document.getElementById("extName");
  const $extVer = document.getElementById("extVer");
  const $storageKey = document.getElementById("storageKey");
  const $btnRefresh = document.getElementById("btnRefresh");
  const $btnClear = document.getElementById("btnClear");

  $extName.textContent = EXT_NAME;
  $extVer.textContent = EXT_VER;
  $storageKey.textContent = STORAGE_KEY;

  function formatBytes(n) {
    if (n == null) return "—";
    if (n < 1024) return `${n} B`;
    if (n < 1024 * 1024) return `${(n / 1024).toFixed(1)} KB`;
    return `${(n / (1024 * 1024)).toFixed(2)} MB`;
  }

  function renderItem(key, data) {
    const card = document.createElement("div");
    card.className = "card";
    // Each field row
    const rows = [
      ["Hotel", data.hotelName || "—"],
      ["Room", data.roomDesc || "—"],
      ["Nights", data.nights || "—"],
      ["Price", data.price || "—"],
      ["Total", data.total || "—"],
      ["Grtot", data.grtot || "—"],
      ["Page URL", data.url || "—"],
      ["Key", key]
    ];
    rows.forEach(([k, v]) => {
      const row = document.createElement("div");
      row.className = "row";
      const kEl = document.createElement("div");
      kEl.className = "k";
      kEl.textContent = k;
      const vEl = document.createElement("div");
      vEl.className = "v";
      if (/^https?:\/\//i.test(v)) {
        const a = document.createElement("a");
        a.href = v;
        a.target = "_blank";
        a.rel = "noreferrer";
        a.textContent = v;
        vEl.appendChild(a);
      } else {
        vEl.textContent = String(v);
      }
      row.appendChild(kEl);
      row.appendChild(vEl);
      card.appendChild(row);
    });
    return card;
  }

  function mapToSortedEntries(map) {
    const entries = Object.entries(map || {});
    // Stable sort: by hotelName, then roomDesc, then key
    entries.sort((a, b) => {
      const A = a[1], B = b[1];
      const hn = (A.hotelName || "").localeCompare(B.hotelName || "");
      if (hn !== 0) return hn;
      const rd = (A.roomDesc || "").localeCompare(B.roomDesc || "");
      if (rd !== 0) return rd;
      return a[0].localeCompare(b[0]);
    });
    return entries;
  }

  function setEmptyState(isEmpty) {
    $empty.hidden = !isEmpty;
    $list.hidden = isEmpty;
  }

  function setCounts(n) {
    $count.textContent = String(n);
    $techCount.textContent = String(n);
  }

  function setLoadedAtNow() {
    const now = new Date();
    $loadedAt.textContent = now.toLocaleString();
  }

  function getBytesForKey(key) {
    return new Promise((resolve) => {
      chrome.storage.local.getBytesInUse([key], (bytes) => {
        resolve(bytes || 0);
      });
    });
  }

  function loadMap() {
    return new Promise((resolve) => {
      chrome.storage.local.get([STORAGE_KEY], (res) => {
        const map = res[STORAGE_KEY] || {};
        info("Loaded selections", { count: Object.keys(map).length, map });
        resolve(map);
      });
    });
  }

  function saveMap(map) {
    return new Promise((resolve) => {
      chrome.storage.local.set({ [STORAGE_KEY]: map }, () => {
        info("Saved selections", { count: Object.keys(map).length });
        resolve();
      });
    });
  }

  async function render() {
    try {
      const map = await loadMap();
      const entries = mapToSortedEntries(map);

      // Counts + bytes
      setCounts(entries.length);
      const bytes = await getBytesForKey(STORAGE_KEY);
      $bytesInUse.textContent = formatBytes(bytes);
      setLoadedAtNow();

      // Empty state
      setEmptyState(entries.length === 0);

      // Render items
      $items.replaceChildren();
      entries.forEach(([key, data]) => {
        const el = renderItem(key, data);
        $items.appendChild(el);
      });

      info("Render complete.", { items: entries.length, bytesInUse: bytes });
    } catch (e) {
      error("Render failed:", e);
    }
  }

  async function clearAll() {
    try {
      await saveMap({});
      await render();
      info("Cleared all selections.");
    } catch (e) {
      error("Clear all failed:", e);
    }
  }

  // Wire up controls
  $btnRefresh.addEventListener("click", () => {
    info("Refresh clicked.");
    render();
  });
  $btnClear.addEventListener("click", () => {
    info("Clear all clicked.");
    clearAll();
  });

  // Live update when content script saves changes
  chrome.storage.onChanged.addListener((changes, area) => {
    if (area === "local" && changes[STORAGE_KEY]) {
      info("Storage changed; re-rendering.", { changes: changes[STORAGE_KEY] });
      render();
    }
  });

  // Initial render
  document.addEventListener("DOMContentLoaded", () => {
    info(`Popup init v${EXT_VER}`);
    render();
  });
})();
